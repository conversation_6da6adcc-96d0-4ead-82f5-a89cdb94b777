import { useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';
import { useNavigate } from 'react-router-dom';

interface SystemTrayManagerProps {
  isAuthenticated: boolean;
}

const SystemTrayManager: React.FC<SystemTrayManagerProps> = ({ isAuthenticated }) => {
  const navigate = useNavigate();

  useEffect(() => {
    // 监听来自系统托盘的导航事件
    const unlisten = listen('navigate', (event) => {
      const path = event.payload as string;
      if (isAuthenticated) {
        navigate(path);
      }
    });

    return () => {
      unlisten.then(fn => fn());
    };
  }, [navigate, isAuthenticated]);

  useEffect(() => {
    // 监听窗口关闭事件，实现隐藏到托盘
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (typeof window !== 'undefined' && window.__TAURI__) {
        event.preventDefault();
        // 隐藏窗口而不是关闭
        invoke('hide_main_window');
        return false;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  // 提供一些窗口控制函数
  const showWindow = async () => {
    try {
      await invoke('show_main_window');
    } catch (error) {
      console.error('显示窗口失败:', error);
    }
  };

  const hideWindow = async () => {
    try {
      await invoke('hide_main_window');
    } catch (error) {
      console.error('隐藏窗口失败:', error);
    }
  };

  // 这个组件不渲染任何UI，只是管理系统托盘相关的逻辑
  return null;
};

export default SystemTrayManager;
