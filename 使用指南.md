# Cocos Initiator 使用指南

## 快速开始

### 1. 安装和启动
1. 下载并安装 Cocos Initiator
2. 首次启动会显示登录界面
3. 使用 Gitea 账号登录或使用演示账号 "demo"/"demo"

### 2. 基础配置
1. 登录后点击"设置"按钮
2. 配置工作路径（项目存储位置）
3. 设置通知方式（可选）
4. 启用右键菜单集成（Windows）

## 主要功能使用

### 项目管理

#### 添加项目
1. **从Gitea克隆**：
   - 在工作区右侧选择要克隆的仓库
   - 点击"克隆并打开"
   - 项目会自动添加到项目列表

2. **手动添加**：
   - 将现有Cocos Creator项目复制到工作路径
   - 刷新项目列表即可自动识别

#### 项目操作
- **启动Cocos Creator**：点击项目卡片的"启动Cocos Creator"按钮
- **选择版本启动**：点击"选择版本启动"手动选择Cocos Creator版本
- **打开文件夹**：点击"备用打开方式"在文件管理器中打开
- **移除项目**：点击垃圾桶图标从列表中移除（不删除文件）

### Cocos Creator 管理

#### 版本检测
1. 点击"Cocos管理"进入管理页面
2. 查看"已安装的版本"部分
3. 点击"刷新"重新检测版本

#### 启动管理
- **直接启动**：点击版本卡片的"启动此版本"
- **项目启动**：在"运行中的项目"部分管理正在运行的项目
- **停止进程**：点击"停止项目"按钮终止Cocos Creator进程

### Git 版本控制

#### 查看状态
- 项目卡片会显示当前Git分支
- 黄色标签表示有未提交更改
- 蓝色标签表示有远程更新

#### Git 操作
- **提交更改**：点击"提交更改"按钮一键提交所有修改
- **拉取更新**：点击"拉取更新"按钮获取远程更新
- **查看详情**：Git状态会实时显示在项目卡片中

### 通知系统

#### 配置通知
1. 进入设置页面
2. 选择通知类型：
   - **系统通知**：使用操作系统原生通知
   - **微信**：配置微信群Webhook URL
   - **飞书**：配置飞书群Webhook URL
3. 点击"测试通知"验证配置

#### 自动通知
系统会在以下情况自动发送通知：
- 项目启动Cocos Creator时
- 项目关闭Cocos Creator时
- 新项目添加到工作区时
- 项目从工作区移除时

### 系统集成

#### 系统托盘
- 关闭主窗口时应用会最小化到系统托盘
- 右键托盘图标可快速访问功能
- 左键托盘图标显示主窗口

#### 右键菜单（Windows）
1. 在设置中启用"注册右键菜单"
2. 在文件夹上右键会显示"用Cocos Initiator打开"选项
3. 可以在设置中随时注销右键菜单

#### 自动更新
- 应用启动时会自动检查更新
- 发现新版本时会显示更新对话框
- 可选择立即更新或稍后提醒

## 高级功能

### 多版本管理
- 支持同时安装多个Cocos Creator版本
- 自动检测项目所需版本并启动对应版本
- 手动选择特定版本启动项目

### 进程监控
- 实时监控Cocos Creator进程状态
- 自动检测进程异常并更新状态
- 支持强制终止无响应进程

### 工作流优化
- 30秒自动刷新项目状态
- 智能状态指示器
- 一键操作简化工作流程

## 故障排除

### 常见问题

#### 1. 检测不到Cocos Creator
**解决方案**：
- 确保Cocos Creator安装在标准路径
- 点击"刷新"按钮重新检测
- 检查安装路径权限

#### 2. 启动失败
**解决方案**：
- 检查Cocos Creator版本是否与项目兼容
- 尝试手动选择其他版本
- 使用"备用打开方式"打开文件夹

#### 3. Git操作失败
**解决方案**：
- 确保项目是有效的Git仓库
- 检查Git配置和权限
- 手动在命令行中执行Git操作

#### 4. 通知发送失败
**解决方案**：
- 检查网络连接
- 验证Webhook URL配置
- 测试系统通知权限

### 日志和调试
- 在开发模式下可以查看控制台日志
- 错误信息会显示在界面上
- 可以通过设置页面测试各项功能

## 最佳实践

### 项目组织
1. 使用统一的工作路径管理所有项目
2. 保持项目名称简洁明了
3. 定期提交代码到Git仓库

### 版本管理
1. 为不同项目使用合适的Cocos Creator版本
2. 保持版本更新但避免频繁切换
3. 在团队中统一Cocos Creator版本

### 团队协作
1. 配置团队通知群组
2. 使用统一的Gitea服务器
3. 建立代码提交规范

### 系统维护
1. 定期检查应用更新
2. 清理不再使用的项目
3. 备份重要项目数据

## 技术支持

### 系统要求
- **Windows**: Windows 10/11
- **macOS**: macOS 10.15+
- **Linux**: Ubuntu 18.04+ 或其他主流发行版

### 依赖软件
- Git（用于版本控制功能）
- Cocos Creator（一个或多个版本）
- 网络连接（用于Gitea和通知功能）

### 获取帮助
- 查看错误信息和提示
- 检查系统日志
- 重启应用尝试解决问题
- 联系技术支持

通过这个使用指南，您可以充分利用 Cocos Initiator 的所有功能，提高 Cocos Creator 项目的开发效率。
