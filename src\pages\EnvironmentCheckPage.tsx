import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';

interface EnvironmentCheckPageProps {
  onEnvironmentReady: () => void;
}

const EnvironmentCheckPage: React.FC<EnvironmentCheckPageProps> = ({ onEnvironmentReady }) => {
  const [envStatus, setEnvStatus] = useState<EnvironmentStatus | null>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkEnvironment = async () => {
    try {
      setIsChecking(true);
      setError(null);
      
      if (typeof window !== 'undefined' && window.__TAURI__) {
        const status = await invoke<EnvironmentStatus>('check_environment');
        setEnvStatus(status);
        
        // 如果环境完整，自动进入工作区
        if (status.all_ready) {
          setTimeout(() => {
            onEnvironmentReady();
          }, 1000);
        }
      } else {
        // 浏览器环境，模拟数据
        const mockStatus: EnvironmentStatus = {
          git_installed: true,
          git_version: '2.34.1',
          nodejs_installed: true,
          nodejs_version: 'v18.17.0',
          npm_installed: true,
          npm_version: '9.6.7',
          all_ready: true
        };
        setEnvStatus(mockStatus);
        setTimeout(() => {
          onEnvironmentReady();
        }, 1000);
      }
    } catch (error: any) {
      setError(error.message || '检查环境失败');
    } finally {
      setIsChecking(false);
    }
  };

  const openInstallationGuide = async (software: string) => {
    try {
      if (typeof window !== 'undefined' && window.__TAURI__) {
        await invoke('open_installation_guide', { software });
      } else {
        // 浏览器环境，直接打开链接
        const urls = {
          git: 'https://git-scm.com/downloads',
          nodejs: 'https://nodejs.org/'
        };
        window.open(urls[software as keyof typeof urls], '_blank');
      }
    } catch (error: any) {
      setError(error.message || '打开安装指南失败');
    }
  };

  useEffect(() => {
    checkEnvironment();
  }, []);

  const getStatusText = (installed: boolean) => {
    return installed ? '已安装' : '未安装';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
      <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl p-8 w-full max-w-3xl border border-white/20">
        <div className="text-center mb-10">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-4">
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent mb-3">
            环境检查
          </h1>
          <p className="text-gray-600 text-lg leading-relaxed max-w-md mx-auto">
            在使用 Cocos Initiator 之前，需要确保您的系统已安装必要的开发环境
          </p>
        </div>

        {error && (
          <div className="bg-red-50/80 backdrop-blur-sm border border-red-200/50 rounded-xl p-4 mb-8 shadow-lg">
            <div className="flex items-center">
              <div className="flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <span className="text-red-700 font-medium">{error}</span>
            </div>
          </div>
        )}

        {isChecking ? (
          <div className="text-center py-12">
            <div className="relative">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-gray-200 mx-auto mb-6"></div>
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-500 border-t-transparent absolute top-0 left-1/2 transform -translate-x-1/2"></div>
            </div>
            <p className="text-gray-600 text-lg font-medium">正在检查环境...</p>
            <p className="text-gray-500 text-sm mt-2">请稍候，这可能需要几秒钟</p>
          </div>
        ) : envStatus ? (
          <div className="space-y-6">
            {/* Git 检查 */}
            <div className="group relative overflow-hidden bg-gradient-to-r from-white to-gray-50/50 rounded-2xl p-6 border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center ${
                    envStatus.git_installed
                      ? 'bg-green-100 text-green-600'
                      : 'bg-red-100 text-red-600'
                  }`}>
                    {envStatus.git_installed ? (
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    )}
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-800 mb-1">Git</h3>
                    <p className="text-gray-600">
                      版本控制系统，用于管理项目代码
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                    envStatus.git_installed
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {getStatusText(envStatus.git_installed)}
                  </div>
                  {envStatus.git_version && (
                    <p className="text-sm text-gray-500 mt-2 font-mono">{envStatus.git_version}</p>
                  )}
                  {!envStatus.git_installed && (
                    <button
                      onClick={() => openInstallationGuide('git')}
                      className="mt-3 px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-sm font-medium rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
                    >
                      安装指南
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* Node.js 检查 */}
            <div className="group relative overflow-hidden bg-gradient-to-r from-white to-gray-50/50 rounded-2xl p-6 border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="absolute inset-0 bg-gradient-to-r from-green-500/5 to-emerald-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center ${
                    envStatus.nodejs_installed
                      ? 'bg-green-100 text-green-600'
                      : 'bg-red-100 text-red-600'
                  }`}>
                    {envStatus.nodejs_installed ? (
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    )}
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-800 mb-1">Node.js</h3>
                    <p className="text-gray-600">
                      JavaScript 运行环境，某些功能需要
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                    envStatus.nodejs_installed
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {getStatusText(envStatus.nodejs_installed)}
                  </div>
                  {envStatus.nodejs_version && (
                    <p className="text-sm text-gray-500 mt-2 font-mono">{envStatus.nodejs_version}</p>
                  )}
                  {!envStatus.nodejs_installed && (
                    <button
                      onClick={() => openInstallationGuide('nodejs')}
                      className="mt-3 px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white text-sm font-medium rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
                    >
                      安装指南
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* npm 检查 */}
            {envStatus.nodejs_installed && (
              <div className="group relative overflow-hidden bg-gradient-to-r from-white to-gray-50/50 rounded-2xl p-6 border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="absolute inset-0 bg-gradient-to-r from-orange-500/5 to-yellow-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center ${
                      envStatus.npm_installed
                        ? 'bg-green-100 text-green-600'
                        : 'bg-red-100 text-red-600'
                    }`}>
                      {envStatus.npm_installed ? (
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      ) : (
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      )}
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-800 mb-1">npm</h3>
                      <p className="text-gray-600">
                        Node.js 包管理器
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                      envStatus.npm_installed
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {getStatusText(envStatus.npm_installed)}
                    </div>
                    {envStatus.npm_version && (
                      <p className="text-sm text-gray-500 mt-2 font-mono">{envStatus.npm_version}</p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* 总体状态 */}
            <div className={`mt-10 p-8 rounded-2xl border-2 shadow-xl transition-all duration-500 ${
              envStatus.all_ready
                ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200/50'
                : 'bg-gradient-to-r from-orange-50 to-yellow-50 border-orange-200/50'
            }`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className={`flex-shrink-0 w-16 h-16 rounded-full flex items-center justify-center ${
                    envStatus.all_ready
                      ? 'bg-green-100 text-green-600'
                      : 'bg-orange-100 text-orange-600'
                  }`}>
                    {envStatus.all_ready ? (
                      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    ) : (
                      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    )}
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-800 mb-2">环境状态</h3>
                    <p className="text-gray-600 text-lg">
                      {envStatus.all_ready
                        ? '🎉 环境检查完成，可以开始使用 Cocos Initiator'
                        : '⚠️ 请安装缺失的软件后重新检查'}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  {envStatus.all_ready ? (
                    <div className="flex flex-col items-end space-y-2">
                      <div className="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full font-semibold">
                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        环境就绪
                      </div>
                      <p className="text-sm text-gray-500">正在跳转到工作区...</p>
                    </div>
                  ) : (
                    <button
                      onClick={checkEnvironment}
                      className="px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white font-medium rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                    >
                      <svg className="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                      重新检查
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* 跳过按钮（仅在某些环境缺失时显示） */}
            {!envStatus.all_ready && (
              <div className="text-center mt-8">
                <div className="inline-flex flex-col items-center space-y-3">
                  <div className="w-px h-8 bg-gradient-to-b from-transparent via-gray-300 to-transparent"></div>
                  <button
                    onClick={onEnvironmentReady}
                    className="group px-6 py-3 text-gray-600 border-2 border-gray-200 rounded-xl hover:border-gray-300 hover:bg-gray-50 transition-all duration-200 font-medium"
                  >
                    <svg className="w-5 h-5 inline mr-2 group-hover:text-orange-500 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 9l3 3m0 0l-3 3m3-3H8m13 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    跳过检查，继续使用
                  </button>
                  <p className="text-xs text-gray-500 max-w-xs">
                    ⚠️ 跳过检查可能导致部分功能不可用
                  </p>
                </div>
              </div>
            )}
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default EnvironmentCheckPage;
