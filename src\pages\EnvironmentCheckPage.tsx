import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/tauri';

interface EnvironmentCheckPageProps {
  onEnvironmentReady: () => void;
}

const EnvironmentCheckPage: React.FC<EnvironmentCheckPageProps> = ({ onEnvironmentReady }) => {
  const [envStatus, setEnvStatus] = useState<EnvironmentStatus | null>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkEnvironment = async () => {
    try {
      setIsChecking(true);
      setError(null);
      
      if (typeof window !== 'undefined' && window.__TAURI__) {
        const status = await invoke<EnvironmentStatus>('check_environment');
        setEnvStatus(status);
        
        // 如果环境完整，自动进入工作区
        if (status.all_ready) {
          setTimeout(() => {
            onEnvironmentReady();
          }, 1000);
        }
      } else {
        // 浏览器环境，模拟数据
        const mockStatus: EnvironmentStatus = {
          git_installed: true,
          git_version: '2.34.1',
          nodejs_installed: true,
          nodejs_version: 'v18.17.0',
          npm_installed: true,
          npm_version: '9.6.7',
          all_ready: true
        };
        setEnvStatus(mockStatus);
        setTimeout(() => {
          onEnvironmentReady();
        }, 1000);
      }
    } catch (error: any) {
      setError(error.message || '检查环境失败');
    } finally {
      setIsChecking(false);
    }
  };

  const openInstallationGuide = async (software: string) => {
    try {
      if (typeof window !== 'undefined' && window.__TAURI__) {
        await invoke('open_installation_guide', { software });
      } else {
        // 浏览器环境，直接打开链接
        const urls = {
          git: 'https://git-scm.com/downloads',
          nodejs: 'https://nodejs.org/'
        };
        window.open(urls[software as keyof typeof urls], '_blank');
      }
    } catch (error: any) {
      setError(error.message || '打开安装指南失败');
    }
  };

  useEffect(() => {
    checkEnvironment();
  }, []);

  const getStatusIcon = (installed: boolean) => {
    return installed ? (
      <span className="text-green-500 text-xl">✓</span>
    ) : (
      <span className="text-red-500 text-xl">✗</span>
    );
  };

  const getStatusText = (installed: boolean) => {
    return installed ? '已安装' : '未安装';
  };

  const getStatusColor = (installed: boolean) => {
    return installed ? 'text-green-600' : 'text-red-600';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl p-8 w-full max-w-2xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">环境检查</h1>
          <p className="text-gray-600">
            在使用 Cocos Initiator 之前，需要确保您的系统已安装必要的开发环境
          </p>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <span className="text-red-500 text-xl mr-2">⚠</span>
              <span className="text-red-700">{error}</span>
            </div>
          </div>
        )}

        {isChecking ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-600">正在检查环境...</p>
          </div>
        ) : envStatus ? (
          <div className="space-y-6">
            {/* Git 检查 */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                {getStatusIcon(envStatus.git_installed)}
                <div>
                  <h3 className="font-semibold text-gray-800">Git</h3>
                  <p className="text-sm text-gray-600">
                    版本控制系统，用于管理项目代码
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className={`font-medium ${getStatusColor(envStatus.git_installed)}`}>
                  {getStatusText(envStatus.git_installed)}
                </p>
                {envStatus.git_version && (
                  <p className="text-sm text-gray-500">{envStatus.git_version}</p>
                )}
                {!envStatus.git_installed && (
                  <button
                    onClick={() => openInstallationGuide('git')}
                    className="mt-2 px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors"
                  >
                    安装指南
                  </button>
                )}
              </div>
            </div>

            {/* Node.js 检查 */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                {getStatusIcon(envStatus.nodejs_installed)}
                <div>
                  <h3 className="font-semibold text-gray-800">Node.js</h3>
                  <p className="text-sm text-gray-600">
                    JavaScript 运行环境，某些功能需要
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className={`font-medium ${getStatusColor(envStatus.nodejs_installed)}`}>
                  {getStatusText(envStatus.nodejs_installed)}
                </p>
                {envStatus.nodejs_version && (
                  <p className="text-sm text-gray-500">{envStatus.nodejs_version}</p>
                )}
                {!envStatus.nodejs_installed && (
                  <button
                    onClick={() => openInstallationGuide('nodejs')}
                    className="mt-2 px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors"
                  >
                    安装指南
                  </button>
                )}
              </div>
            </div>

            {/* npm 检查 */}
            {envStatus.nodejs_installed && (
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(envStatus.npm_installed)}
                  <div>
                    <h3 className="font-semibold text-gray-800">npm</h3>
                    <p className="text-sm text-gray-600">
                      Node.js 包管理器
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`font-medium ${getStatusColor(envStatus.npm_installed)}`}>
                    {getStatusText(envStatus.npm_installed)}
                  </p>
                  {envStatus.npm_version && (
                    <p className="text-sm text-gray-500">{envStatus.npm_version}</p>
                  )}
                </div>
              </div>
            )}

            {/* 总体状态 */}
            <div className="mt-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-800">环境状态</h3>
                  <p className="text-gray-600">
                    {envStatus.all_ready 
                      ? '环境检查完成，可以开始使用 Cocos Initiator' 
                      : '请安装缺失的软件后重新检查'}
                  </p>
                </div>
                <div className="text-right">
                  {envStatus.all_ready ? (
                    <div className="flex items-center space-x-2">
                      <span className="text-green-500 text-2xl">✓</span>
                      <span className="text-green-600 font-medium">就绪</span>
                    </div>
                  ) : (
                    <button
                      onClick={checkEnvironment}
                      className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                    >
                      重新检查
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* 跳过按钮（仅在某些环境缺失时显示） */}
            {!envStatus.all_ready && (
              <div className="text-center mt-6">
                <button
                  onClick={onEnvironmentReady}
                  className="px-6 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50 transition-colors"
                >
                  跳过检查，继续使用（部分功能可能不可用）
                </button>
              </div>
            )}
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default EnvironmentCheckPage;
