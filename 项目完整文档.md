# Cocos Initiator 项目完整文档

## 目录
1. [项目需求](#项目需求)
2. [功能实现说明](#功能实现说明)
3. [Cocos Creator功能实现说明](#cocos-creator功能实现说明)
4. [全功能完成总结](#全功能完成总结)
5. [完成总结](#完成总结)
6. [使用指南](#使用指南)
7. [功能测试指南](#功能测试指南)
8. [环境检查功能分析](#环境检查功能分析)

---

## 项目需求

### 需求背景

这个项目我采用的技术栈是​​Rust + Tauri + React​， 我的需求背景是：我是一个游戏程序，我要和一个不懂cocos的游戏美术合作开发一个游戏，我希望能开发一个软件协同这个过程

### 需求详情

界面：
1. 有一个登录界面，界面有登录账号，登录密码，账号密码是我部署在服务器上的服务，gitea自己部署的一个仓库管理，校验是发送账号密码去校验，如果错误提示错误，正确则保留账号密码在本地方便后续提交
2. 有一个设置界面，需要登录后才能进行设置，
设置内容选项有：
· 自动设置是否开机启动软件
· 设置微信/飞书/qq 方便用于信息通知（只能选择1项，会检测对应配置是否填写了，如果没有填写授权就不进行通知）
· 默认的工作路径（即自动克隆仓库的路径）
3. 有一个工作界面，元素列表显示当前打开的coco项目，点击对应选择框切换当前打开的对应cocos项目编辑器，如果没有打开的项目，则会显示一个下拉框，下拉这个账户在我创建的gitea仓库中加入的组织中可见的仓库列表，

功能：
· 这个软件有后台功能，关闭ui会自动在后台常驻，除非退出
· 能发布自动更新软件的功能（登录校验账户成功后开始检查软件更新）
· 自动注册表，功能是右键打开文件夹为cocos项目（快速根据配置打开对应cocos编辑器进行开发，前提要通过子账号验证，否则会弹出软件要求进行登录）
· 自动注册表，功能是克隆cocos项目（也是会校验gitea账户，然后快速弹出软件，并打开工作界面，然后自动创建一个item,）
· 登录后会去申请可以创建克隆的项目，如果失败则可以选择的账户为空（不可创建打开）
· **选择一个有权限的仓库后，会首先检测工作路径是否有对应已经拷贝克隆的仓库，如果没有则会先进行git拷贝工程（如果没有安装git&node，则会自动/弹出安装git&node），等这些都搞完后，会检测对应要打开的cocosCreator项目对应的cocos编辑器是否有安装，如果没有安装会弹出cocos面板，让用户自己安装，然后提示用户自己重新尝试打开**
· 退出cocos打开的项目会提示用户是否自动一键add所有修改，然后push (可以后台设置对应参数)
· 自动监听打开项目的仓库是否有最新的git提交（当前分支），如果有测提示用户进行拉取，同意则自动关闭打开的对应编辑器，然后自动开始拉取项目同步，显示ui，完成后则重新自动打开项目

### 整理的需求

#### 一、用户认证模块
**登录界面**
- 组件：账号输入框、密码输入框、登录按钮、错误提示区
- 流程：
  - 提交凭证到自部署Gitea服务验证（Rust端使用reqwest调用Gitea API）
  - 成功后将凭证加密存储（使用tauri-plugin-store）
  - 失败时显示具体错误原因（如"账号不存在"、"密码错误"）

**凭证管理**
- 自动加载本地存储的凭证（启动时检查）
- 支持注销功能（清除本地凭证）

#### 二、设置模块（需登录后访问）
**系统设置**
- 开机自启：调用tauri-plugin-autostart
- 默认工作路径：文件夹选择器组件（Rust端集成tauri-plugin-dialog）

**通知设置**
- 单选按钮组（微信/飞书/QQ）
- 动态表单：选择平台后显示对应配置项（如微信Webhook URL）
- 实时验证：点击"测试通知"按钮发送验证消息

**Git行为设置**
- 退出时自动提交：开关按钮（默认开启）
- 自定义提交消息模板：文本输入框

#### 三、核心工作流模块
**仓库管理**
```mermaid
graph TD
    A[选择仓库] --> B{本地是否存在？}
    B -->|否| C[安装Git/Node检测]
    C --> D[执行git clone]
    B -->|是| E[检查Cocos编辑器]
    E --> F{版本匹配？}
    F -->|否| G[打开安装面板]
    F -->|是| H[git pull最新代码]
    H --> I[启动Cocos Creator]
```

**关键功能**
- 项目列表：实时显示已打开项目状态（运行中/已关闭）
- 项目卡片包含：仓库名、分支状态、最后提交时间
- 编辑器集成：动态检测Cocos安装路径（Windows注册表/macOS应用目录）
- 通过std::process::Command启动编辑器
- 进程监控：使用sysinfo包跟踪编辑器状态

**自动化Git操作**
- 项目关闭时：自动git add . + git commit -m <模板>
- 冲突处理：生成差异报告供用户决策
- 定时轮询：每10分钟检查远程更新（通过cron调度）

#### 四、系统集成模块
**右键菜单注册（Windows/macOS）**
- 使用tauri-plugin-shell注册系统命令
- 功能项：
  - "作为Cocos项目打开" → 启动软件并加载项目
  - "克隆Cocos仓库" → 唤起仓库选择界面

**后台服务**
- 系统托盘支持（tauri-plugin-tray）
- 事件监听：
  - 编辑器进程退出触发提交
  - 网络状态变化重试失败操作

**自动更新**
- 集成tauri-plugin-updater
- 登录成功后检查版本差异
- 支持静默后台更新

#### 五、异常处理机制
**依赖检测**
- Git未安装：引导下载安装包（调用tauri::api::shell::open）
- Node.js缺失：提供版本管理工具链接
- Cocos版本不匹配：显示推荐版本下载入口

**网络错误处理**
- Gitea连接失败：缓存操作队列
- 通知发送失败：本地日志记录+重试机制

**冲突解决**
- 可视化差异对比（集成react-diff-viewer）
- 手动解决模式：暂停自动同步流程

---

## 环境检查功能分析

### 当前实现状态

经过代码分析，发现项目**缺少对Git和Node.js环境的检查功能**。虽然需求文档中明确提到了这个功能，但在当前的实现中并没有找到相关代码。

### 缺失的功能

1. **Git环境检查**
   - 检查系统是否安装Git
   - 检查Git版本是否满足要求
   - 未安装时提供安装引导

2. **Node.js环境检查**
   - 检查系统是否安装Node.js
   - 检查Node.js版本是否满足要求
   - 未安装时提供安装引导

3. **环境检查时机**
   - 应用启动时检查
   - 克隆仓库前检查
   - 项目操作前检查

### 当前Git使用情况

项目中确实使用了Git命令，但没有事先检查Git是否安装：

```rust
// 在多个地方直接调用git命令，但没有检查Git是否安装
let output = Command::new("git")
    .args(&["status", "--porcelain"])
    .current_dir(&path)
    .output()
    .map_err(|e| format!("执行git status失败: {}", e))?;
```

如果系统没有安装Git，这些命令会失败，但错误信息不够友好。

### 需要添加的功能

根据需求文档，需要实现以下环境检查功能：

1. **环境检测命令**
   - `check_git_installation()` - 检查Git是否安装
   - `check_nodejs_installation()` - 检查Node.js是否安装
   - `get_git_version()` - 获取Git版本
   - `get_nodejs_version()` - 获取Node.js版本

2. **安装引导功能**
   - `guide_git_installation()` - 引导用户安装Git
   - `guide_nodejs_installation()` - 引导用户安装Node.js
   - 打开官方下载页面
   - 显示安装说明

3. **前端界面**
   - 环境检查状态显示
   - 安装引导对话框
   - 重新检查按钮

### 已实现的环境检查功能 ✅

#### 后端实现 (Rust)

**新增命令函数：**
1. `check_environment()` - 全面检查开发环境
   - 检查Git是否安装及版本
   - 检查Node.js是否安装及版本
   - 检查npm是否安装及版本
   - 返回环境完整性状态

2. `open_installation_guide(software)` - 打开安装指南
   - 支持Git和Node.js的官方下载页面
   - 跨平台浏览器打开支持

**环境检查逻辑：**
- 在克隆仓库前自动检查Git环境
- 如果Git未安装，阻止克隆操作并提示用户
- 提供友好的错误信息和安装指导

#### 前端实现 (React + TypeScript)

**新增组件：**
1. `EnvironmentCheckPage` - 环境检查页面
   - 显示Git、Node.js、npm的安装状态
   - 实时版本信息显示
   - 一键安装指南链接
   - 重新检查功能
   - 跳过检查选项（部分功能受限）

**用户体验优化：**
- 登录后自动进行环境检查
- 直观的状态指示器（✓/✗）
- 彩色状态显示（绿色/红色）
- 安装指南按钮
- 环境就绪后自动跳转

#### 工作流程

1. **用户登录** → 显示环境检查页面
2. **自动检测** → 扫描系统环境
3. **状态显示** → 显示各组件安装状态
4. **引导安装** → 提供安装指南链接
5. **重新检查** → 安装后可重新检测
6. **环境就绪** → 自动进入工作区

#### 安全保障

- 在所有Git操作前检查环境
- 友好的错误提示和解决方案
- 支持跳过检查（功能受限模式）
- 跨平台兼容性

---

## 功能实现说明

### 已完成的功能

#### 1. 用户认证模块 ✅
- **快速登录界面**: 当检测到本地存储的登录凭证时，显示一键登录选项
- **完整登录界面**: 提供账号密码输入，支持Gitea API认证
- **凭证管理**: 使用tauri-plugin-store安全存储登录凭证
- **自动登录检测**: 启动时检查本地凭证，提供快速登录选项

#### 2. 设置模块 ✅
- **系统设置**: 开机自启动选项
- **通知设置**: 支持微信/飞书/QQ通知配置，包含测试通知功能
- **工作路径设置**: 文件夹选择器，设置默认克隆路径
- **设置持久化**: 所有设置保存到本地JSON文件

#### 3. 工作区模块 ✅
- **项目列表显示**: 显示已克隆的Cocos项目
- **仓库管理**: 从Gitea获取用户有权限的仓库列表
- **项目克隆**: 一键克隆选中的仓库到本地
- **项目状态管理**: 跟踪项目运行状态
- **自动刷新**: 每30秒自动刷新项目状态

#### 4. Git集成功能 ✅
- **Git状态检查**: 实时显示项目的Git状态
  - 当前分支信息
  - 未提交更改提示
  - 远程更新提示
- **自动提交**: 一键提交所有更改
- **拉取更新**: 一键拉取远程更新
- **状态可视化**: 用颜色和图标显示Git状态

#### 5. 项目管理功能 ✅
- **项目打开**: 打开项目文件夹（跨平台支持）
- **项目移除**: 从列表中移除项目（不删除文件）
- **项目信息显示**: 显示项目路径、最后提交时间等
- **项目状态跟踪**: 运行中/已关闭状态管理

### 技术实现亮点

#### 后端 (Rust + Tauri)
1. **模块化设计**: 将认证、设置、项目管理等功能分离
2. **错误处理**: 完善的错误处理和用户友好的错误消息
3. **跨平台支持**: 使用条件编译支持Windows/macOS/Linux
4. **安全存储**: 使用tauri-plugin-store安全存储敏感信息
5. **Git集成**: 直接调用Git命令进行版本控制操作

#### 前端 (React + TypeScript)
1. **组件化设计**: 创建可复用的ProjectCard组件
2. **状态管理**: 使用React Hooks管理复杂状态
3. **用户体验**: 加载状态、错误提示、成功反馈
4. **响应式设计**: 使用Tailwind CSS实现美观的界面
5. **类型安全**: 完整的TypeScript类型定义

### 当前功能演示

#### 登录流程
1. 启动应用时检查本地凭证
2. 如有凭证，显示快速登录界面
3. 可选择一键登录或登录其他账户
4. 支持清除本地登录信息

#### 项目管理流程
1. 登录后进入工作区
2. 查看已有项目列表，包含Git状态信息
3. 从下拉列表选择Gitea仓库进行克隆
4. 对项目进行Git操作（提交、拉取）
5. 打开或移除项目

#### 设置管理
1. 配置系统设置（自启动）
2. 设置通知方式并测试
3. 选择默认工作路径
4. 所有设置自动保存

### 待实现功能

#### 高优先级
1. **环境检查**: 检查Git和Node.js是否安装 ✅ **已完成**
2. **Cocos Creator检测**: 检测系统中安装的Cocos Creator版本 ✅ **已完成**
3. **编辑器启动**: 根据项目版本启动对应的Cocos Creator ✅ **已完成**
4. **进程监控**: 监控Cocos Creator进程状态 ✅ **已完成**
5. **自动更新**: 软件自动更新功能 ✅ **已完成**

#### 中优先级
1. **右键菜单注册**: 文件夹右键菜单集成 ✅ **已完成**
2. **系统托盘**: 后台运行和托盘图标 ✅ **已完成**
3. **通知实现**: 实际的微信/飞书/QQ通知发送 ✅ **已完成**
4. **Git冲突处理**: 可视化Git冲突解决

#### 低优先级
1. **项目模板**: 预设的Cocos项目模板
2. **插件系统**: 支持第三方插件
3. **团队协作**: 多人协作功能
4. **性能监控**: 项目构建性能监控

### 开发环境要求

#### 必需
- Node.js 16+
- Rust 1.70+
- Git

#### 可选
- Cocos Creator (用于测试项目打开功能)

### 运行说明

```bash
# 安装依赖
npm install

# 开发模式运行前端
npm run dev

# 开发模式运行Tauri应用
npm run tauri dev

# 构建生产版本
npm run tauri build
```

### 注意事项

1. **Git依赖**: 所有Git功能需要系统安装Git ✅ **已添加检查**
2. **Node.js依赖**: 某些功能可能需要Node.js ✅ **已添加检查**
3. **权限要求**: 某些功能可能需要管理员权限
4. **网络连接**: Gitea API调用需要网络连接
5. **路径限制**: 项目路径不能包含特殊字符

### 总结

当前版本已经实现了需求文档中的大部分核心功能，包括：
- ✅ 完整的用户认证流程
- ✅ 一键登录功能
- ✅ 设置管理和持久化
- ✅ 项目管理和Git集成
- ✅ 美观的用户界面
- ✅ Cocos Creator集成
- ✅ 系统集成功能

**所有核心功能均已实现**，包括Git和Node.js环境检查功能。

---
