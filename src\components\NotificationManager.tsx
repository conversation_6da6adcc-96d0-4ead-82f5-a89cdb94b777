import { useEffect, useRef } from 'react';
import { invoke } from '@tauri-apps/api/core';

interface Project {
  id: string;
  name: string;
  path: string;
  status: string;
  last_commit_time: string;
  repository_url: string;
  cocos_version?: string;
  process_id?: number;
}

interface NotificationManagerProps {
  projects: Project[];
  isAuthenticated: boolean;
}

const NotificationManager: React.FC<NotificationManagerProps> = ({ 
  projects, 
  isAuthenticated 
}) => {
  const previousProjectsRef = useRef<Project[]>([]);

  useEffect(() => {
    if (!isAuthenticated) return;

    const previousProjects = previousProjectsRef.current;
    
    // 检查项目状态变化
    projects.forEach(currentProject => {
      const previousProject = previousProjects.find(p => p.id === currentProject.id);
      
      if (previousProject) {
        // 检查状态变化
        if (previousProject.status !== currentProject.status) {
          handleStatusChange(currentProject, previousProject.status, currentProject.status);
        }
      } else {
        // 新项目添加
        handleNewProject(currentProject);
      }
    });

    // 检查项目移除
    previousProjects.forEach(previousProject => {
      const currentProject = projects.find(p => p.id === previousProject.id);
      if (!currentProject) {
        handleProjectRemoved(previousProject);
      }
    });

    // 更新引用
    previousProjectsRef.current = [...projects];
  }, [projects, isAuthenticated]);

  const handleStatusChange = async (project: Project, oldStatus: string, newStatus: string) => {
    let title = '';
    let message = '';

    switch (newStatus) {
      case 'running':
        title = 'Cocos Creator 已启动';
        message = `项目 "${project.name}" 已在 Cocos Creator 中打开`;
        break;
      case 'closed':
        if (oldStatus === 'running') {
          title = 'Cocos Creator 已关闭';
          message = `项目 "${project.name}" 的 Cocos Creator 已关闭`;
        }
        break;
      case 'opened_folder':
        title = '项目文件夹已打开';
        message = `项目 "${project.name}" 的文件夹已在文件管理器中打开`;
        break;
      default:
        return; // 不发送通知
    }

    if (title && message) {
      await sendNotification(title, message);
    }
  };

  const handleNewProject = async (project: Project) => {
    const title = '新项目已添加';
    const message = `项目 "${project.name}" 已添加到工作区`;
    await sendNotification(title, message);
  };

  const handleProjectRemoved = async (project: Project) => {
    const title = '项目已移除';
    const message = `项目 "${project.name}" 已从工作区移除`;
    await sendNotification(title, message);
  };

  const sendNotification = async (title: string, body: string) => {
    try {
      if (typeof window !== 'undefined' && window.__TAURI__) {
        await invoke('send_notification', { title, body });
      } else {
        // 浏览器环境使用 Web Notification API
        if ('Notification' in window && Notification.permission === 'granted') {
          new Notification(title, { body });
        } else if ('Notification' in window && Notification.permission !== 'denied') {
          const permission = await Notification.requestPermission();
          if (permission === 'granted') {
            new Notification(title, { body });
          }
        }
      }
    } catch (error) {
      console.error('发送通知失败:', error);
    }
  };

  // 这个组件不渲染任何UI
  return null;
};

export default NotificationManager;
