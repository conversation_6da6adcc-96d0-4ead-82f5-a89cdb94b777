import { useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';

// 本地存储键名
const NOTIFIED_PROJECTS_KEY = 'cocos_initiator_notified_projects';
const PREVIOUS_PROJECTS_KEY = 'cocos_initiator_previous_projects';

// 获取已通知的项目列表
const getNotifiedProjects = (): string[] => {
  try {
    const stored = localStorage.getItem(NOTIFIED_PROJECTS_KEY);
    return stored ? JSON.parse(stored) : [];
  } catch {
    return [];
  }
};

// 保存已通知的项目
const saveNotifiedProjects = (projectIds: string[]) => {
  try {
    localStorage.setItem(NOTIFIED_PROJECTS_KEY, JSON.stringify(projectIds));
  } catch (error) {
    console.error('保存通知记录失败:', error);
  }
};

// 获取之前的项目列表
const getPreviousProjects = (): Project[] => {
  try {
    const stored = localStorage.getItem(PREVIOUS_PROJECTS_KEY);
    return stored ? JSON.parse(stored) : [];
  } catch {
    return [];
  }
};

// 保存当前项目列表
const savePreviousProjects = (projects: Project[]) => {
  try {
    localStorage.setItem(PREVIOUS_PROJECTS_KEY, JSON.stringify(projects));
  } catch (error) {
    console.error('保存项目列表失败:', error);
  }
};

// 清除通知记录（登出时调用）
export const clearNotificationHistory = () => {
  try {
    localStorage.removeItem(NOTIFIED_PROJECTS_KEY);
    localStorage.removeItem(PREVIOUS_PROJECTS_KEY);
  } catch (error) {
    console.error('清除通知记录失败:', error);
  }
};

interface Project {
  id: string;
  name: string;
  path: string;
  status: string;
  last_commit_time: string;
  repository_url: string;
  cocos_version?: string;
  process_id?: number;
}

interface NotificationManagerProps {
  projects: Project[];
  isAuthenticated: boolean;
}

const NotificationManager: React.FC<NotificationManagerProps> = ({
  projects,
  isAuthenticated
}) => {
  useEffect(() => {
    if (!isAuthenticated) return;

    const previousProjects = getPreviousProjects();
    const notifiedProjects = getNotifiedProjects();

    // 检查项目状态变化
    projects.forEach(currentProject => {
      const previousProject = previousProjects.find((p: Project) => p.id === currentProject.id);

      if (previousProject) {
        // 检查状态变化
        if (previousProject.status !== currentProject.status) {
          handleStatusChange(currentProject, previousProject.status, currentProject.status);
        }
      } else {
        // 新项目添加 - 只有未通知过的项目才发送通知
        if (!notifiedProjects.includes(currentProject.id)) {
          handleNewProject(currentProject);
          // 标记为已通知
          const updatedNotified = [...notifiedProjects, currentProject.id];
          saveNotifiedProjects(updatedNotified);
        }
      }
    });

    // 检查项目移除
    previousProjects.forEach((previousProject: Project) => {
      const currentProject = projects.find(p => p.id === previousProject.id);
      if (!currentProject) {
        handleProjectRemoved(previousProject);
        // 从已通知列表中移除
        const updatedNotified = notifiedProjects.filter(id => id !== previousProject.id);
        saveNotifiedProjects(updatedNotified);
      }
    });

    // 保存当前项目列表
    savePreviousProjects(projects);
  }, [projects, isAuthenticated]);

  const handleStatusChange = async (project: Project, oldStatus: string, newStatus: string) => {
    let title = '';
    let message = '';

    switch (newStatus) {
      case 'running':
        title = 'Cocos Creator 已启动';
        message = `项目 "${project.name}" 已在 Cocos Creator 中打开`;
        break;
      case 'closed':
        if (oldStatus === 'running') {
          title = 'Cocos Creator 已关闭';
          message = `项目 "${project.name}" 的 Cocos Creator 已关闭`;
        }
        break;
      case 'opened_folder':
        title = '项目文件夹已打开';
        message = `项目 "${project.name}" 的文件夹已在文件管理器中打开`;
        break;
      default:
        return; // 不发送通知
    }

    if (title && message) {
      await sendNotification(title, message);
    }
  };

  const handleNewProject = async (project: Project) => {
    const title = '新项目已添加';
    const message = `项目 "${project.name}" 已添加到工作区`;
    await sendNotification(title, message);
  };

  const handleProjectRemoved = async (project: Project) => {
    const title = '项目已移除';
    const message = `项目 "${project.name}" 已从工作区移除`;
    await sendNotification(title, message);
  };

  const sendNotification = async (title: string, body: string) => {
    try {
      if (typeof window !== 'undefined' && window.__TAURI__) {
        await invoke('send_notification', { title, body });
      } else {
        // 浏览器环境使用 Web Notification API
        if ('Notification' in window && Notification.permission === 'granted') {
          new Notification(title, { body });
        } else if ('Notification' in window && Notification.permission !== 'denied') {
          const permission = await Notification.requestPermission();
          if (permission === 'granted') {
            new Notification(title, { body });
          }
        }
      }
    } catch (error) {
      console.error('发送通知失败:', error);
    }
  };

  // 这个组件不渲染任何UI
  return null;
};

export default NotificationManager;
