import { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';

interface UpdateInfo {
  available: boolean;
  version?: string;
  notes?: string;
  downloadUrl?: string;
}

interface UpdateManagerProps {
  isAuthenticated: boolean;
}

const UpdateManager: React.FC<UpdateManagerProps> = ({ isAuthenticated }) => {
  const [updateInfo, setUpdateInfo] = useState<UpdateInfo | null>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [showUpdateDialog, setShowUpdateDialog] = useState(false);

  useEffect(() => {
    // 应用启动时检查更新
    if (isAuthenticated) {
      checkForUpdates();
    }
  }, [isAuthenticated]);

  const checkForUpdates = async () => {
    try {
      setIsChecking(true);
      
      if (typeof window !== 'undefined' && window.__TAURI__) {
        const result = await invoke<string>('check_for_updates');
        
        // 解析更新信息
        if (result.includes('最新版本')) {
          setUpdateInfo({ available: false });
        } else {
          // 这里应该解析实际的更新信息
          setUpdateInfo({
            available: true,
            version: '1.1.0',
            notes: '修复了一些bug，增加了新功能',
            downloadUrl: 'https://example.com/update'
          });
          setShowUpdateDialog(true);
        }
      }
    } catch (error) {
      console.error('检查更新失败:', error);
    } finally {
      setIsChecking(false);
    }
  };

  const downloadUpdate = async () => {
    try {
      setIsDownloading(true);
      
      if (typeof window !== 'undefined' && window.__TAURI__) {
        // 这里实现实际的下载逻辑
        await new Promise(resolve => setTimeout(resolve, 3000)); // 模拟下载
        
        // 下载完成后提示重启
        if (confirm('更新下载完成，是否立即重启应用？')) {
          // 重启应用
          await invoke('restart_app');
        }
      }
    } catch (error) {
      console.error('下载更新失败:', error);
      alert('下载更新失败，请稍后重试');
    } finally {
      setIsDownloading(false);
    }
  };

  if (!showUpdateDialog || !updateInfo?.available) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex items-center mb-4">
          <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
            <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900">发现新版本</h3>
        </div>

        <div className="mb-4">
          <p className="text-sm text-gray-600 mb-2">
            新版本 <span className="font-medium">{updateInfo.version}</span> 已发布
          </p>
          {updateInfo.notes && (
            <div className="bg-gray-50 rounded p-3">
              <p className="text-sm text-gray-700">{updateInfo.notes}</p>
            </div>
          )}
        </div>

        <div className="flex space-x-3">
          <button
            onClick={() => setShowUpdateDialog(false)}
            className="flex-1 btn-secondary text-sm"
            disabled={isDownloading}
          >
            稍后提醒
          </button>
          <button
            onClick={downloadUpdate}
            disabled={isDownloading}
            className="flex-1 btn-primary text-sm"
          >
            {isDownloading ? (
              <div className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                下载中...
              </div>
            ) : (
              '立即更新'
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default UpdateManager;
